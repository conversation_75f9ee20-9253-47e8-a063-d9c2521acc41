import axios, { type AxiosInstance, type AxiosResponse } from 'axios'

class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      config => {
        const token = localStorage.getItem('token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      error => {
        return Promise.reject(error)
      }
    )

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      response => response,
      error => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/auth/login', credentials)
    return response.data
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/auth/register', userData)
    return response.data
  }

  // User endpoints
  async getUsers(): Promise<ApiResponse<User[]>> {
    const response: AxiosResponse<ApiResponse<User[]>> = await this.api.get('/users')
    return response.data
  }

  async getUser(id: string): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.api.get(`/users/${id}`)
    return response.data
  }

  async createUser(userData: CreateUserRequest): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.api.post('/users', userData)
    return response.data
  }

  async updateUser(id: string, userData: UpdateUserRequest): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.api.put(`/users/${id}`, userData)
    return response.data
  }

  async deleteUser(id: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/users/${id}`)
    return response.data
  }

  // Group endpoints
  async getGroups(): Promise<ApiResponse<Group[]>> {
    const response: AxiosResponse<ApiResponse<Group[]>> = await this.api.get('/groups')
    return response.data
  }

  async getGroup(id: string): Promise<ApiResponse<Group>> {
    const response: AxiosResponse<ApiResponse<Group>> = await this.api.get(`/groups/${id}`)
    return response.data
  }

  async createGroup(groupData: CreateGroupRequest): Promise<ApiResponse<Group>> {
    const response: AxiosResponse<ApiResponse<Group>> = await this.api.post('/groups', groupData)
    return response.data
  }

  async updateGroup(id: string, groupData: UpdateGroupRequest): Promise<ApiResponse<Group>> {
    const response: AxiosResponse<ApiResponse<Group>> = await this.api.put(`/groups/${id}`, groupData)
    return response.data
  }

  async deleteGroup(id: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/groups/${id}`)
    return response.data
  }

  async assignUsersToGroup(groupId: string, userIds: AssignUsersToGroupRequest): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.post(`/groups/${groupId}/users`, userIds)
    return response.data
  }

  async removeUserFromGroup(groupId: string, userId: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/groups/${groupId}/users/${userId}`)
    return response.data
  }

  // Role endpoints
  async getRoles(): Promise<ApiResponse<Role[]>> {
    const response: AxiosResponse<ApiResponse<Role[]>> = await this.api.get('/roles')
    return response.data
  }

  async getRole(id: string): Promise<ApiResponse<Role>> {
    const response: AxiosResponse<ApiResponse<Role>> = await this.api.get(`/roles/${id}`)
    return response.data
  }

  async createRole(roleData: CreateRoleRequest): Promise<ApiResponse<Role>> {
    const response: AxiosResponse<ApiResponse<Role>> = await this.api.post('/roles', roleData)
    return response.data
  }

  async updateRole(id: string, roleData: UpdateRoleRequest): Promise<ApiResponse<Role>> {
    const response: AxiosResponse<ApiResponse<Role>> = await this.api.put(`/roles/${id}`, roleData)
    return response.data
  }

  async deleteRole(id: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/roles/${id}`)
    return response.data
  }

  async assignRolesToGroup(groupId: string, roleIds: AssignRolesToGroupRequest): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.post(`/groups/${groupId}/roles`, roleIds)
    return response.data
  }

  async removeRoleFromGroup(groupId: string, roleId: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/groups/${groupId}/roles/${roleId}`)
    return response.data
  }

  // Module endpoints
  async getModules(): Promise<ApiResponse<Module[]>> {
    const response: AxiosResponse<ApiResponse<Module[]>> = await this.api.get('/modules')
    return response.data
  }

  async getModule(id: string): Promise<ApiResponse<Module>> {
    const response: AxiosResponse<ApiResponse<Module>> = await this.api.get(`/modules/${id}`)
    return response.data
  }

  async createModule(moduleData: CreateModuleRequest): Promise<ApiResponse<Module>> {
    const response: AxiosResponse<ApiResponse<Module>> = await this.api.post('/modules', moduleData)
    return response.data
  }

  async updateModule(id: string, moduleData: UpdateModuleRequest): Promise<ApiResponse<Module>> {
    const response: AxiosResponse<ApiResponse<Module>> = await this.api.put(`/modules/${id}`, moduleData)
    return response.data
  }

  async deleteModule(id: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/modules/${id}`)
    return response.data
  }

  // Permission endpoints
  async getPermissions(): Promise<ApiResponse<Permission[]>> {
    const response: AxiosResponse<ApiResponse<Permission[]>> = await this.api.get('/permissions')
    return response.data
  }

  async getPermission(id: string): Promise<ApiResponse<Permission>> {
    const response: AxiosResponse<ApiResponse<Permission>> = await this.api.get(`/permissions/${id}`)
    return response.data
  }

  async createPermission(permissionData: CreatePermissionRequest): Promise<ApiResponse<Permission>> {
    const response: AxiosResponse<ApiResponse<Permission>> = await this.api.post('/permissions', permissionData)
    return response.data
  }

  async updatePermission(id: string, permissionData: UpdatePermissionRequest): Promise<ApiResponse<Permission>> {
    const response: AxiosResponse<ApiResponse<Permission>> = await this.api.put(`/permissions/${id}`, permissionData)
    return response.data
  }

  async deletePermission(id: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/permissions/${id}`)
    return response.data
  }

  async assignPermissionsToRole(roleId: string, permissionIds: AssignPermissionsToRoleRequest): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.post(`/roles/${roleId}/permissions`, permissionIds)
    return response.data
  }

  async removePermissionFromRole(roleId: string, permissionId: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/roles/${roleId}/permissions/${permissionId}`)
    return response.data
  }

  // Access control endpoints
  async getUserPermissions(): Promise<ApiResponse<UserPermissions>> {
    const response: AxiosResponse<ApiResponse<UserPermissions>> = await this.api.get('/me/permissions')
    return response.data
  }

  async simulateAction(permissionCheck: PermissionCheck): Promise<ApiResponse<SimulationResponse>> {
    const response: AxiosResponse<ApiResponse<SimulationResponse>> = await this.api.post(
      '/simulate-action',
      permissionCheck
    )
    return response.data
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.get('/health')
    return response.data
  }
}

export const apiService = new ApiService()
export default apiService
