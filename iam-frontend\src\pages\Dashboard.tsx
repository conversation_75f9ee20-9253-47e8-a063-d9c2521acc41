import React, { useState } from 'react'
import { useAuth } from '../hooks/useAuth'
import { apiService } from '../services/api'

const Dashboard: React.FC = () => {
  const { user, permissions } = useAuth()
  const [simulationResult, setSimulationResult] = useState<{ allowed: boolean; message: string } | null>(null)
  const [simulationForm, setSimulationForm] = useState<PermissionCheck>({
    module: '',
    action: ''
  })
  const [isSimulating, setIsSimulating] = useState(false)
  const [quickTestResults, setQuickTestResults] = useState<Record<string, { allowed: boolean; message: string }>>({})
  const [isQuickTesting, setIsQuickTesting] = useState<Record<string, boolean>>({})

  // Predefined modules and actions for quick testing
  const predefinedModules = ['Users', 'Groups', 'Roles', 'Modules', 'Permissions', 'Reports']
  const predefinedActions = ['create', 'read', 'update', 'delete']

  const handleSimulateAction = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!simulationForm.module || !simulationForm.action) {
      setSimulationResult({
        allowed: false,
        message: 'Please provide both module and action'
      })
      return
    }

    setIsSimulating(true)
    try {
      const response = await apiService.simulateAction(simulationForm)
      if (response.success && response.data && response.data.simulation) {
        const hasPermission = response.data.simulation.hasPermission
        setSimulationResult({
          allowed: hasPermission,
          message: hasPermission
            ? `✅ Access granted for ${simulationForm.action} on ${simulationForm.module}`
            : `❌ Access denied for ${simulationForm.action} on ${simulationForm.module}`
        })
      } else {
        setSimulationResult({
          allowed: false,
          message: 'Failed to simulate action'
        })
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error && 'response' in error
          ? (error as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Error simulating action'
      setSimulationResult({
        allowed: false,
        message: errorMessage || 'Error simulating action'
      })
    } finally {
      setIsSimulating(false)
    }
  }

  const handleQuickTest = async (module: string, action: string) => {
    const testKey = `${module}-${action}`
    setIsQuickTesting(prev => ({ ...prev, [testKey]: true }))

    try {
      const response = await apiService.simulateAction({ module, action })
      if (response.success && response.data && response.data.simulation) {
        const hasPermission = response.data.simulation.hasPermission
        setQuickTestResults(prev => ({
          ...prev,
          [testKey]: {
            allowed: hasPermission,
            message: hasPermission ? `✅ Access granted` : `❌ Access denied`
          }
        }))
      } else {
        setQuickTestResults(prev => ({
          ...prev,
          [testKey]: {
            allowed: false,
            message: 'Failed to test'
          }
        }))
      }
    } catch {
      setQuickTestResults(prev => ({
        ...prev,
        [testKey]: {
          allowed: false,
          message: 'Error testing'
        }
      }))
    } finally {
      setIsQuickTesting(prev => ({ ...prev, [testKey]: false }))
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setSimulationForm(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const clearAllResults = () => {
    setQuickTestResults({})
    setSimulationResult(null)
  }

  const testAllPermissions = async () => {
    const allTests = []
    for (const module of predefinedModules) {
      for (const action of predefinedActions) {
        allTests.push({ module, action })
      }
    }

    // Set all as loading
    const loadingState = allTests.reduce((acc, { module, action }) => {
      acc[`${module}-${action}`] = true
      return acc
    }, {} as Record<string, boolean>)
    setIsQuickTesting(loadingState)

    // Test all permissions concurrently
    const results = await Promise.allSettled(
      allTests.map(async ({ module, action }) => {
        try {
          const response = await apiService.simulateAction({ module, action })
          const hasPermission =
            response.success && response.data && response.data.simulation
              ? response.data.simulation.hasPermission
              : false
          return {
            key: `${module}-${action}`,
            result: {
              allowed: hasPermission,
              message: hasPermission ? '✅ Access granted' : '❌ Access denied'
            }
          }
        } catch {
          return {
            key: `${module}-${action}`,
            result: {
              allowed: false,
              message: 'Error testing'
            }
          }
        }
      })
    )

    // Update results
    const newResults = results.reduce((acc, result) => {
      if (result.status === 'fulfilled') {
        acc[result.value.key] = result.value.result
      }
      return acc
    }, {} as Record<string, { allowed: boolean; message: string }>)

    setQuickTestResults(newResults)
    setIsQuickTesting({})
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome, {user?.firstName} {user?.lastName}!
        </h1>
        <p className="text-gray-600">Welcome to the Identity and Access Management System dashboard.</p>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Column - User Info and Permissions */}
        <div className="lg:col-span-3 space-y-6">
          {/* User Info */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Your Account Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Username</label>
                <p className="mt-1 text-sm text-gray-900">{user?.username}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <p className="mt-1 text-sm text-gray-900">{user?.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <p className="mt-1 text-sm">
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {user?.isActive ? 'Active' : 'Inactive'}
                  </span>
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Member Since</label>
                <p className="mt-1 text-sm text-gray-900">
                  {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                </p>
              </div>
            </div>
          </div>

          {/* Permissions Section */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Your Permissions</h2>
            {permissions && permissions.permissions && permissions.permissions.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {permissions.permissions.map((permission: { module: string; action: string }, index: number) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                    <div className="text-sm font-medium text-gray-900">{permission.module}</div>
                    <div className="text-sm text-gray-600 capitalize">{permission.action}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
                <p className="mt-2 text-gray-500">No permissions assigned.</p>
              </div>
            )}
          </div>

          {/* Permission Simulation */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-medium text-gray-900">Test Permission Access</h2>
              <button onClick={clearAllResults} className="text-sm text-gray-500 hover:text-gray-700 underline">
                Clear Results
              </button>
            </div>
            <p className="text-sm text-gray-600 mb-6">
              Use this tool to test if you have access to perform specific actions on modules.
            </p>

            {/* Custom Permission Test Form */}
            <form onSubmit={handleSimulateAction} className="space-y-4 mb-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="module" className="form-label">
                    Module
                  </label>
                  <select
                    id="module"
                    name="module"
                    className="form-input"
                    value={simulationForm.module}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select a module</option>
                    {predefinedModules.map(module => (
                      <option key={module} value={module}>
                        {module}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label htmlFor="action" className="form-label">
                    Action
                  </label>
                  <select
                    id="action"
                    name="action"
                    className="form-input"
                    value={simulationForm.action}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select an action</option>
                    {predefinedActions.map(action => (
                      <option key={action} value={action}>
                        {action.charAt(0).toUpperCase() + action.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <button
                type="submit"
                disabled={isSimulating}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSimulating ? 'Testing...' : 'Test Permission'}
              </button>
            </form>

            {simulationResult && (
              <div
                className={`mb-6 p-4 rounded-md ${
                  simulationResult.allowed ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                }`}
              >
                <p className={`text-sm ${simulationResult.allowed ? 'text-green-700' : 'text-red-700'}`}>
                  {simulationResult.message}
                </p>
              </div>
            )}

            {/* Quick Permission Tests */}
            <div className="border-t pt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-md font-medium text-gray-900">Quick Permission Tests</h3>
                <button
                  onClick={testAllPermissions}
                  disabled={Object.values(isQuickTesting).some(Boolean)}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  {Object.values(isQuickTesting).some(Boolean) ? 'Testing All...' : 'Test All Permissions'}
                </button>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Click any button below to quickly test common permission combinations, or use "Test All" to check
                everything at once.
              </p>

              <div className="space-y-4">
                {predefinedModules.map(module => (
                  <div key={module} className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <span className="mr-2">📦</span>
                      {module} Module
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {predefinedActions.map(action => {
                        const testKey = `${module}-${action}`
                        const result = quickTestResults[testKey]
                        const isLoading = isQuickTesting[testKey]

                        return (
                          <button
                            key={action}
                            onClick={() => handleQuickTest(module, action)}
                            disabled={isLoading}
                            className={`px-3 py-2 text-xs font-medium rounded-md transition-colors duration-200 ${
                              result
                                ? result.allowed
                                  ? 'bg-green-100 text-green-800 border border-green-200'
                                  : 'bg-red-100 text-red-800 border border-red-200'
                                : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                            } disabled:opacity-50 disabled:cursor-not-allowed`}
                          >
                            {isLoading ? (
                              <span className="flex items-center">
                                <svg className="animate-spin -ml-1 mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24">
                                  <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                  ></circle>
                                  <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                  ></path>
                                </svg>
                                ...
                              </span>
                            ) : (
                              <>
                                {result && (result.allowed ? '✅ ' : '❌ ')}
                                {action.charAt(0).toUpperCase() + action.slice(1)}
                              </>
                            )}
                          </button>
                        )
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Quick Stats */}
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Stats</h3>
            <div className="space-y-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-medium">👥</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-500">Total Permissions</div>
                    <div className="text-2xl font-bold text-gray-900">{permissions?.permissions?.length || 0}</div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-medium">✅</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-500">Account Status</div>
                    <div className="text-2xl font-bold text-gray-900">{user?.isActive ? 'Active' : 'Inactive'}</div>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-medium">🔐</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-500">Access Level</div>
                    <div className="text-2xl font-bold text-gray-900">
                      {permissions?.permissions?.length ? 'Granted' : 'Limited'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Test Results Summary */}
          {Object.keys(quickTestResults).length > 0 && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Test Results Summary</h3>
              {/* <div className="space-y-3">
                {Object.entries(quickTestResults).map(([testKey, result]) => {
                  const [module, action] = testKey.split('-')
                  return (
                    <div
                      key={testKey}
                      className={`flex items-center justify-between p-3 rounded-md ${
                        result.allowed ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                      }`}
                    >
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-gray-900">
                          {action.charAt(0).toUpperCase() + action.slice(1)} {module}
                        </span>
                      </div>
                      <span className={`text-sm ${result.allowed ? 'text-green-700' : 'text-red-700'}`}>
                        {result.allowed ? '✅ Allowed' : '❌ Denied'}
                      </span>
                    </div>
                  )
                })}
              </div> */}

              {/* Summary Stats */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="bg-green-50 rounded-lg p-3">
                    <div className="text-lg font-bold text-green-700">
                      {Object.values(quickTestResults).filter(r => r.allowed).length}
                    </div>
                    <div className="text-xs text-green-600">Allowed</div>
                  </div>
                  <div className="bg-red-50 rounded-lg p-3">
                    <div className="text-lg font-bold text-red-700">
                      {Object.values(quickTestResults).filter(r => !r.allowed).length}
                    </div>
                    <div className="text-xs text-red-600">Denied</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
